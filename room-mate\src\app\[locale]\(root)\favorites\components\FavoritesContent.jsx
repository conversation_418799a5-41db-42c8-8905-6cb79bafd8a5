'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { TypeToggle } from '@/components/homepage/type-toggle'
import { useFavoriteProperties } from '@/hooks/useFavoriteProperties'
import { useFavoritePersons } from '@/hooks/useFavoritePersons'
import { toggleFavorite as togglePropertyFavorite } from '@/actions/properties'
import { toggleFavorite as togglePersonFavorite } from '@/actions/persons'
import { PropertyCard } from '@/components/homepage/property-card'
// import { PersonCard } from '@/components/homepage/person-card'
import { cn } from '@/lib/utils'

// Helper function to get sort parameters
function getSortParams(sortBy) {
  switch (sortBy) {
    case 'newest':
      return { sortBy: 'createdAt', sortOrder: 'desc' }
    case 'oldest':
      return { sortBy: 'createdAt', sortOrder: 'asc' }
    case 'price-high':
      return { sortBy: 'price', sortOrder: 'desc' }
    case 'price-low':
      return { sortBy: 'price', sortOrder: 'asc' }
    case 'popular':
      return { sortBy: 'views', sortOrder: 'desc' }
    default:
      return { sortBy: 'createdAt', sortOrder: 'desc' }
  }
}

export function FavoritesContent() {
  const t = useTranslations('homepage')
  const queryClient = useQueryClient()
  const [viewType, setViewType] = useState('properties')
  const [currentPage, setCurrentPage] = useState(1)
  const [sortBy] = useState('newest')
  const [viewMode] = useState('grid')

  const { isPropertyFavorite } = useFavoriteProperties()
  const { isPersonFavorite } = useFavoritePersons()

  const sortParams = getSortParams(sortBy)

  // Properties query
  const propertiesQuery = useQuery({
    queryKey: ['properties', selectedCategory, currentPage, sortBy],
    queryFn: () => getProperties({
      page: currentPage,
      limit: 12,
      categoryId: selectedCategory === 'all' ? '' : selectedCategory,
      ...sortParams
    }),
    enabled: viewType === 'properties',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Persons query
  const personsQuery = useQuery({
    queryKey: ['persons', selectedCategory, currentPage, sortBy],
    queryFn: () => getPersons({
      page: currentPage,
      limit: 12,
      categoryId: selectedCategory === 'all' ? '' : selectedCategory,
      ...sortParams
    }),
    enabled: viewType === 'persons',
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Mutations for favorite toggle
  const togglePropertyFavoriteMutation = useMutation({
    mutationFn: (id) => togglePropertyFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch properties data and favorites
      queryClient.invalidateQueries({ queryKey: ['properties'] })
      queryClient.invalidateQueries({ queryKey: ['favorites', 'properties'] })
    },
    onError: (error) => {
      console.error('Failed to toggle property favorite:', error)
    }
  })

  const togglePersonFavoriteMutation = useMutation({
    mutationFn: (id) => togglePersonFavorite(id),
    onSuccess: () => {
      // Invalidate and refetch persons data and favorites
      queryClient.invalidateQueries({ queryKey: ['persons'] })
      queryClient.invalidateQueries({ queryKey: ['favorites', 'persons'] })
    },
    onError: (error) => {
      console.error('Failed to toggle person favorite:', error)
    }
  })

  // Handle different API response structures
  const currentQuery = viewType === 'properties' ? propertiesQuery : personsQuery
  const apiResponse = currentQuery.data?.data
  const currentData = viewType === 'properties'
    ? apiResponse?.data || []
    : apiResponse?.data?.persons || []
  const currentPagination = viewType === 'properties'
    ? apiResponse?.pagination
    : apiResponse?.data?.pagination

  // Handle favorite toggle
  const handleFavoriteToggle = (id) => {
    if (viewType === 'properties') {
      togglePropertyFavoriteMutation.mutate(id)
    } else {
      togglePersonFavoriteMutation.mutate(id)
    }
  }

  // Handle load more
  const handleLoadMore = () => {
    if (currentPagination?.hasNext) {
      setCurrentPage(prev => prev + 1)
    }
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <TypeToggle
          value={viewType}
          onChange={setViewType}
          options={[
            { value: 'properties', label: t('properties') },
            { value: 'persons', label: t('persons') },
          ]}
        />
        <Button
          onClick={handleLoadMore}
          disabled={!currentPagination?.hasNext}
        >
          {t('loadMore')}
        </Button>
      </div>
      <div className={cn(
        "grid gap-6",
        viewMode === 'grid'
          ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
          : "grid-cols-1"
      )}>
        {Array.isArray(currentData) && currentData.map((item) => (
          viewType === 'properties' ? (
            <PropertyCard
              key={item.id}
              property={item}
              onFavoriteToggle={handleFavoriteToggle}
              viewType={viewType}
              isFavoriteLoading={togglePropertyFavoriteMutation.isPending}
              isFavorite={isPropertyFavorite(item.id)}
            />
          ) : (
            <PersonCard
              key={item.id}
              person={item}
              onFavoriteToggle={handleFavoriteToggle}
              viewType={viewType}
              isFavoriteLoading={togglePersonFavoriteMutation.isPending}
              isFavorite={isPersonFavorite(item.id)}
            />
          )
        ))}
      </div>
    </div>
  )
}